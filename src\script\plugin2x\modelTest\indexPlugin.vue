<template>
    <div class="model-test-container">
        <header class="model-test-container-header">大模型调用测试</header>
        <main class="model-test-container-main">
            <!-- 侧边栏组件 -->
            <Sidebar
                :menu-groups="menuGroups"
                :active-menu-item="activeMenuItem"
                :is-ai-responding="isAiResponding"
                :is-stream-response="isStreamResponse"
                @new-chat="handleNewChat"
                @search-select="handleSearchSelect"
                @menu-item-click="handleMenuItemClick"
                @delete-session="handleDeleteSession"
                @stop-generation="handleStopGeneration"
            />

            <!-- 聊天页面组件 -->
            <ChatPage
                :title="currentSessionTitle"
                :session-id="currentSessionId"
                :messages="chatMessages"
                :model-options="modelOptions"
                :defaultModel="defaultModel"
                :selectedModel="selectedModel"
                :is-ai-responding="isAiResponding"
                :is-stream-response="isStreamResponse"
                :chat-storage-service="chatStorageService"
                :is-storage-available="isStorageAvailable"
                @send-message="handleSendMessage"
                @model-change="handleModelChange"
                @stop-generation="handleStopGeneration"
                @title-updated="handleTitleUpdated"
            />
        </main>
    </div>
</template>

<script>
import Sidebar from '@/script/components/chat/Sidebar.vue';
import ChatPage from '@/script/components/chat/ChatPage.vue';
import { AIService, ChatStorageService } from '@/script/services';
import appConfig from '@/script/constant/appConfig';
import { getModelList, getDefaultModel, isModelExists } from '@/script/constant/aiModelConfig';
import { DataTransformer, MessageModel } from '@/script/constant/dataModels';

export default {
    name: 'ModelTestPage',
    components: {
        Sidebar,
        ChatPage
    },
    data() {
        return {
            // 服务实例
            aiService: null,
            chatStorageService: null,

            // 当前会话信息
            currentSessionId: null,
            currentSessionTitle: '新对话',

            // 消息数据
            chatMessages: [],

            // 模型配置 - 使用扁平化配置文件
            modelOptions: getModelList().map((model) => ({
                value: model.id,
                label: model.name,
                desc: model.description,
                provider: model.provider
            })),
            defaultModel: getDefaultModel(),
            selectedModel: getDefaultModel(), // 初始值，会在created中更新

            // 状态管理
            isAiResponding: false,
            isStreamResponse: false,
            isStorageAvailable: false,

            // 侧边栏数据
            menuGroups: [],
            activeMenuItem: null,

            // 配置 - 使用配置文件
            config: {
                storageType: 'localStorage', // 默认使用localStorage
                enableAutoSave: appConfig.ui.message.enableMarkdown,
                maxHistoryLength: appConfig.ui.message.maxHistoryLength
            }
        };
    },

    async created() {
        // 首先恢复用户上次选择的模型
        this.restoreSelectedModel();

        await this.initializeServices();
        await this.loadInitialData();
    },

    mounted() {
        this.$nextTick(() => {
            setTimeout(() => {
                this.validateAndSyncModel();
            }, 1000);
        });
    },

    async beforeDestroy() {
        // 在页面卸载前清理当前的空会话
        if (this.currentSessionId) {
            await this.cleanupEmptySession(this.currentSessionId);
        }
        this.destroyServices();
    },

    methods: {
        // ==================== 空会话管理方法 ====================

        /**
         * 检查会话是否为空（没有消息）
         */
        async isEmptySession(sessionId) {
            if (!this.isStorageAvailable || !sessionId) {
                return false;
            }

            try {
                const messages = await this.chatStorageService.getMessages(sessionId);
                return !messages || messages.length === 0;
            } catch (error) {
                return false;
            }
        },

        /**
         * 清理空会话
         */
        async cleanupEmptySession(sessionId) {
            if (!sessionId || sessionId === this.currentSessionId) {
                return false;
            }

            try {
                const isEmpty = await this.isEmptySession(sessionId);
                if (isEmpty) {
                    await this.chatStorageService.deleteSession(sessionId);
                    return true;
                }
                return false;
            } catch (error) {
                return false;
            }
        },

        /**
         * 在会话切换时清理之前的空会话
         */
        async cleanupPreviousEmptySession(previousSessionId) {
            if (!previousSessionId || previousSessionId === this.currentSessionId) {
                return;
            }

            const wasDeleted = await this.cleanupEmptySession(previousSessionId);
            if (wasDeleted) {
                // 刷新会话列表以移除已删除的空会话
                await this.loadSessionList();
            }
        },

        /**
         * 批量清理所有空会话（应用启动时调用）
         */
        async cleanupAllEmptySessions() {
            if (!this.isStorageAvailable) {
                return;
            }

            try {
                const sessions = await this.chatStorageService.getAllSessions();
                let hasDeleted = false;

                for (const session of sessions) {
                    // 不删除当前会话
                    if (session.id === this.currentSessionId) {
                        // eslint-disable-next-line no-continue
                        continue;
                    }

                    const isEmpty = await this.isEmptySession(session.id);
                    if (isEmpty) {
                        await this.chatStorageService.deleteSession(session.id);
                        hasDeleted = true;
                    }
                }

                // 如果删除了会话，刷新会话列表
                if (hasDeleted) {
                    await this.loadSessionList();
                }
            } catch (error) {
                // 静默处理错误
            }
        },

        // ==================== 模型同步验证方法 ====================

        /**
         * 验证模型同步状态
         */
        validateModelSync() {
            if (!this.aiService || !this.aiService.config) {
                return false;
            }

            const uiModel = this.selectedModel;
            const serviceModel = this.aiService.config.model;

            return uiModel === serviceModel;
        },

        /**
         * 强制同步模型状态
         */
        async forceModelSync() {
            if (!this.validateModelSync()) {
                await this.reinitializeAIService();
            }
        },

        // ==================== 模型持久化方法 ====================

        /**
         * 获取存储的模型
         */
        getStoredModel() {
            try {
                const storedModel = localStorage.getItem('selectedModel');
                if (storedModel && isModelExists(storedModel)) {
                    return storedModel;
                }
            } catch (error) {
                // 静默处理错误
            }
            return null;
        },

        /**
         * 保存选择的模型
         */
        saveSelectedModel(modelId) {
            try {
                localStorage.setItem('selectedModel', modelId);
            } catch (error) {
                // 静默处理错误
            }
        },

        /**
         * 恢复选择的模型（在created中调用）
         */
        restoreSelectedModel() {
            const storedModel = this.getStoredModel();
            if (storedModel) {
                this.selectedModel = storedModel;
            }
        },

        /**
         * 验证并同步模型状态
         */
        validateAndSyncModel() {
            const storedModel = this.getStoredModel();
            const currentModel = this.selectedModel;
            let aiServiceModel = null;
            if (this.aiService && this.aiService.config) {
                aiServiceModel = this.aiService.config.model;
            }

            // 检查是否存在不一致
            const modelsMatch = storedModel === currentModel && currentModel === aiServiceModel;

            if (!modelsMatch) {
                // 优先使用存储的模型（用户最后的选择）
                const targetModel = storedModel || currentModel || getDefaultModel();

                if (isModelExists(targetModel)) {
                    this.selectedModel = targetModel;
                    this.saveSelectedModel(targetModel);

                    // 重新初始化AIService
                    if (this.aiService && this.aiService.config.model !== targetModel) {
                        this.reinitializeAIService();
                    }
                } else {
                    this.validateSelectedModel();
                }
            }
        },

        // ==================== 初始化方法 ====================

        /**
         * 初始化服务
         */
        async initializeServices() {
            try {
                // 首先验证和同步模型状态
                this.validateAndSyncModel();

                // 验证和修正模型配置
                this.validateSelectedModel();

                // 初始化存储服务
                this.chatStorageService = new ChatStorageService({
                    storageType: this.config.storageType,
                    maxSessions: 100,
                    autoCleanup: true
                });

                this.isStorageAvailable = await this.chatStorageService.init();

                // 初始化AI服务
                this.aiService = new AIService({
                    model: this.selectedModel
                });
            } catch (error) {
                this.$message.error('服务初始化失败，部分功能可能不可用');
            }
        },

        /**
         * 验证选择的模型
         */
        validateSelectedModel() {
            // 检查当前选择的模型是否存在于配置中
            if (!isModelExists(this.selectedModel)) {
                const oldModel = this.selectedModel;
                let newModel = null;

                // 优先选择第一个可用模型
                if (this.modelOptions.length > 0) {
                    newModel = this.modelOptions[0].value;
                } else {
                    // 如果modelOptions为空，使用配置文件中的默认模型
                    newModel = getDefaultModel();
                }

                // 再次验证新选择的模型是否存在
                if (!isModelExists(newModel)) {
                    // 如果默认模型也不存在，尝试从AI_MODELS中获取第一个可用模型
                    const availableModels = getModelList();
                    if (availableModels.length > 0) {
                        newModel = availableModels[0].id;
                    } else {
                        return false;
                    }
                }

                // 更新模型
                this.selectedModel = newModel;
                this.defaultModel = newModel;

                // 保存新模型到localStorage
                this.saveSelectedModel(newModel);

                // 显示切换提示
                if (this.$message) {
                    this.$message.warning(
                        `检测到模型 "${oldModel}" 不可用，已自动切换到 "${newModel}"`
                    );
                }

                // 重新初始化AI服务
                this.reinitializeAIService();

                return true; // 表示发生了模型切换
            }
            return false; // 表示没有发生模型切换
        },

        /**
         * 重新初始化AI服务
         */
        async reinitializeAIService() {
            try {
                this.aiService = new AIService({
                    model: this.selectedModel
                });
            } catch (error) {
                if (this.$message) {
                    this.$message.error('AI服务初始化失败');
                }
            }
        },

        /**
         * 加载初始数据
         */
        async loadInitialData() {
            try {
                // 加载会话列表
                await this.loadSessionList();

                // 清理所有空会话
                await this.cleanupAllEmptySessions();

                // 检查是否有当前会话ID
                const currentSessionId = this.chatStorageService.getCurrentSessionId();

                if (
                    currentSessionId &&
                    this.menuGroups[0] &&
                    this.menuGroups[0].items.find((item) => item.id === currentSessionId)
                ) {
                    // 加载当前会话
                    await this.loadSession(currentSessionId);
                } else if (this.menuGroups.length === 0 || !this.menuGroups[0].items.length) {
                    // 如果没有会话，创建新对话
                    await this.handleNewChat();
                } else {
                    // 加载最近的会话
                    const recentSession = this.menuGroups[0].items[0];
                    await this.loadSession(recentSession.id);
                }
            } catch (error) {
                // 创建新对话作为fallback
                await this.handleNewChat();
            }
        },

        /**
         * 加载会话列表
         */
        async loadSessionList() {
            if (!this.isStorageAvailable) {
                this.menuGroups = [];
                return;
            }

            try {
                const sessions = await this.chatStorageService.getAllSessions();
                // 使用统一数据转换器生成菜单组
                this.menuGroups = DataTransformer.sessionsToMenuGroups(sessions);
            } catch (error) {
                this.menuGroups = [];
            }
        },

        // ==================== 会话管理方法 ====================

        /**
         * 加载指定会话
         */
        async loadSession(sessionId) {
            try {
                if (!this.isStorageAvailable) {
                    this.chatMessages = [];
                    return;
                }

                const session = await this.chatStorageService.getSession(sessionId);
                if (!session) {
                    return;
                }

                this.currentSessionId = sessionId;
                this.currentSessionTitle = session.title;

                // 验证会话中保存的模型是否仍然可用
                const sessionModel = session.model || this.defaultModel;
                if (isModelExists(sessionModel)) {
                    // 如果会话模型与当前选择的模型不同，需要切换
                    if (this.selectedModel !== sessionModel) {
                        this.selectedModel = sessionModel;
                        this.saveSelectedModel(sessionModel);

                        // 重新初始化AI服务以使用会话的模型
                        // eslint-disable-next-line max-depth
                        if (this.aiService) {
                            await this.reinitializeAIService();
                        }
                    }
                } else {
                    // 更新会话的模型为当前可用的模型
                    if (this.isStorageAvailable) {
                        await this.chatStorageService.updateSession(sessionId, {
                            model: this.selectedModel
                        });
                    }
                }

                this.activeMenuItem = sessionId;

                // 设置为当前会话
                this.chatStorageService.setCurrentSessionId(sessionId);

                // 加载消息（使用统一数据模型）
                const messages = await this.chatStorageService.getMessages(sessionId);
                this.chatMessages = DataTransformer.messagesToDisplayFormat(messages);

                // 最终验证模型同步状态
                await this.forceModelSync();
            } catch (error) {
                this.$message.error('加载会话失败');
            }
        },

        /**
         * 创建新会话
         */
        async createNewSession() {
            try {
                if (!this.isStorageAvailable) {
                    // 如果存储不可用，只在内存中创建
                    this.currentSessionId = this.generateTempId();
                    this.currentSessionTitle = '新对话';
                    this.chatMessages = [];
                    this.activeMenuItem = this.currentSessionId;
                    return this.currentSessionId;
                }

                const session = await this.chatStorageService.createSession({
                    title: '新对话',
                    model: this.selectedModel
                });

                this.currentSessionId = session.id;
                this.currentSessionTitle = session.title;
                this.chatMessages = [];
                this.activeMenuItem = session.id;

                // 刷新会话列表
                await this.loadSessionList();

                return session.id;
            } catch (error) {
                this.$message.error('创建会话失败');
                return null;
            }
        },

        /**
         * 生成临时ID
         */
        generateTempId() {
            return 'temp_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
        },

        // ==================== 事件处理方法 ====================

        /**
         * 处理新对话
         */
        async handleNewChat() {
            const previousSessionId = this.currentSessionId;
            await this.createNewSession();

            // 清理之前的空会话
            if (previousSessionId) {
                await this.cleanupPreviousEmptySession(previousSessionId);
            }
        },

        /**
         * 处理菜单项点击（会话切换）
         */
        async handleMenuItemClick(item) {
            if (item.type === 'session' && item.id !== this.currentSessionId) {
                const previousSessionId = this.currentSessionId;
                await this.loadSession(item.id);

                // 清理之前的空会话
                if (previousSessionId) {
                    await this.cleanupPreviousEmptySession(previousSessionId);
                }
            }
        },

        /**
         * 处理搜索选择
         */
        async handleSearchSelect(item) {
            await this.handleMenuItemClick(item);
        },

        /**
         * 处理删除会话
         */
        async handleDeleteSession(sessionId) {
            try {
                if (!this.isStorageAvailable) {
                    this.$message.warning('存储服务不可用');
                    return;
                }

                await this.chatStorageService.deleteSession(sessionId);

                // 如果删除的是当前会话，创建新对话
                if (sessionId === this.currentSessionId) {
                    await this.handleNewChat();
                }

                // 刷新会话列表
                await this.loadSessionList();

                this.$message.success('会话已删除');
            } catch (error) {
                this.$message.error('删除会话失败');
            }
        },

        /**
         * 处理模型切换
         */
        async handleModelChange(model) {
            // 验证模型是否存在
            if (!isModelExists(model)) {
                // 重新验证当前模型，如果当前模型也无效，自动修正
                this.validateSelectedModel();
                return;
            }

            const oldModel = this.selectedModel;
            this.selectedModel = model;

            // 保存模型选择到localStorage
            this.saveSelectedModel(model);

            // 重新初始化AI服务以使用新模型
            try {
                this.aiService = new AIService({
                    model: model
                });
            } catch (error) {
                // 切换失败，恢复原模型
                this.selectedModel = oldModel;
                this.saveSelectedModel(oldModel);
                return;
            }

            // 更新当前会话的模型
            if (this.currentSessionId && this.isStorageAvailable) {
                try {
                    await this.chatStorageService.updateSession(this.currentSessionId, {
                        model: model
                    });
                } catch (error) {
                    // 静默处理错误
                }
            }
        },

        /**
         * 处理标题更新
         */
        async handleTitleUpdated(newTitle) {
            this.currentSessionTitle = newTitle;

            if (this.currentSessionId && this.isStorageAvailable) {
                try {
                    await this.chatStorageService.updateSession(this.currentSessionId, {
                        title: newTitle
                    });

                    // 刷新会话列表
                    await this.loadSessionList();
                } catch (error) {
                    if (this.$message) {
                        this.$message.error('更新标题失败，请重试');
                    }
                }
            }
        },

        // ==================== 消息处理方法 ====================

        /**
         * 处理发送消息
         */
        async handleSendMessage(messageData) {
            if (!messageData.text.trim()) {
                return;
            }

            // 防止重复发送
            if (this.isAiResponding) {
                return;
            }

            try {
                // 确保有当前会话
                if (!this.currentSessionId) {
                    await this.createNewSession();
                }

                // 生成唯一的消息ID
                const messageId = this.generateMessageId();

                // 创建用户消息（使用统一数据模型）
                const userMessage = MessageModel.create({
                    id: messageId,
                    role: 'user',
                    content: messageData.text,
                    sessionId: this.currentSessionId
                });

                // 转换为显示格式并添加到消息列表
                this.chatMessages.push(MessageModel.toDisplayFormat(userMessage));

                // 保存用户消息到存储
                if (this.isStorageAvailable) {
                    await this.chatStorageService.addMessage(this.currentSessionId, userMessage);
                }

                // 生成AI响应消息ID
                const assistantMessageId = this.generateMessageId();

                // 创建AI响应消息（使用统一数据模型）
                const assistantMessage = MessageModel.create({
                    id: assistantMessageId,
                    role: 'assistant',
                    content: '',
                    sessionId: this.currentSessionId,
                    model: this.selectedModel,
                    metadata: { isStreaming: true }
                });

                // 转换为显示格式并添加到消息列表
                const displayAssistantMessage = MessageModel.toDisplayFormat(assistantMessage);
                displayAssistantMessage.isStreaming = true; // 添加显示状态
                this.chatMessages.push(displayAssistantMessage);
                const messageIndex = this.chatMessages.length - 1;

                // 设置AI响应状态
                this.isAiResponding = true;
                this.isStreamResponse = true;

                // 准备历史消息（排除刚添加的用户消息和AI响应消息）
                const history = this.chatMessages.slice(0, -2).map((msg) => ({
                    role: msg.role,
                    content: msg.content
                }));

                // 发送AI请求
                await this.aiService.sendMessage(
                    {
                        ...messageData,
                        content: messageData.text,
                        model: this.selectedModel,
                        history: history
                    },
                    {
                        stream: true,
                        onChunk: (_, fullContent) => {
                            // 更新消息内容（统一更新两个字段以保持兼容性）
                            this.$set(this.chatMessages[messageIndex], 'text', fullContent);
                            this.$set(this.chatMessages[messageIndex], 'content', fullContent);
                        },
                        onComplete: async (fullContent) => {
                            // 完成响应（统一更新内容字段）
                            this.chatMessages[messageIndex].text = fullContent;
                            this.chatMessages[messageIndex].content = fullContent;
                            this.chatMessages[messageIndex].isStreaming = false;
                            this.isAiResponding = false;
                            this.isStreamResponse = false;

                            // 保存AI响应到存储
                            if (this.isStorageAvailable) {
                                const completeAssistantMessage = {
                                    ...assistantMessage,
                                    content: fullContent,
                                    isStreaming: false
                                };
                                await this.chatStorageService.addMessage(
                                    this.currentSessionId,
                                    completeAssistantMessage
                                );
                            }

                            // 如果是第一条消息，自动生成标题
                            if (
                                this.chatMessages.length === 2 &&
                                this.currentSessionTitle === '新对话'
                            ) {
                                const title = this.generateSessionTitle(userMessage.content);
                                await this.handleTitleUpdated(title);
                            }
                        },
                        onError: (error) => {
                            // 错误处理
                            this.isAiResponding = false;
                            this.isStreamResponse = false;

                            const errorMessage = this.aiService.handleError(error);
                            // 同时更新text和content字段
                            this.chatMessages[messageIndex].text = `错误: ${errorMessage}`;
                            this.chatMessages[messageIndex].content = `错误: ${errorMessage}`;
                            this.chatMessages[messageIndex].isStreaming = false; // 标记流式输出结束
                            this.chatMessages[messageIndex].isError = true;

                            this.$message.error(errorMessage);
                        }
                    }
                );
            } catch (error) {
                this.isAiResponding = false;
                this.isStreamResponse = false;
                this.$message.error('发送消息失败');
            }
        },

        /**
         * 处理停止生成
         */
        handleStopGeneration() {
            if (this.aiService) {
                this.aiService.stopGeneration();
                this.isAiResponding = false;
                this.isStreamResponse = false;
            }
        },

        /**
         * 生成会话标题
         */
        generateSessionTitle(firstMessage) {
            const title = firstMessage.trim().substring(0, 20);
            if (firstMessage.length > 20) {
                return title + '...';
            }
            return title;
        },

        /**
         * 生成唯一的消息ID
         */
        generateMessageId() {
            return 'msg_' + Date.now().toString(36) + '_' + Math.random().toString(36).substring(2);
        },

        /**
         * 检查并修复模型配置
         * 在模型配置可能发生变化时调用
         */
        checkAndFixModelConfiguration() {
            // 验证当前选择的模型
            const modelChanged = this.validateSelectedModel();

            // 如果模型发生了变化，需要重新初始化AI服务
            if (modelChanged && this.aiService) {
                this.reinitializeAIService();
            }

            return modelChanged;
        },

        // ==================== 清理方法 ====================

        /**
         * 销毁服务实例
         */
        destroyServices() {
            if (this.aiService) {
                this.aiService.destroy();
                this.aiService = null;
            }

            if (this.chatStorageService) {
                this.chatStorageService.destroy();
                this.chatStorageService = null;
            }
        }
    }
};
</script>

<style scoped lang="less">
.model-test-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 1.25rem;
        margin-left: 0.5rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
    }
    &-main {
        min-height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
    }
}
</style>
