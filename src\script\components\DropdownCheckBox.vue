<template>
    <el-popover
        class="dropdown-checkbox"
        :class="{ 'is-active': isOpen }"
        placement="bottom"
        trigger="manual"
        v-model="isOpen"
        width="auto"
        popper-class="checkbox-popover"
    >
        <div class="dropdown-item">
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll"
                ><span class="checkbox-label">全选</span></el-checkbox
            >
            <el-checkbox-group
                class="checkbox-group"
                v-model="checkedItems"
                @change="handleCheckedItemsChange"
            >
                <el-checkbox
                    v-for="(item, index) in filteredOptions"
                    :label="item.name"
                    :key="index"
                >
                    <span
                        class="checkbox-label"
                        :title="item.name"
                        v-html="highlightText(item.name)"
                    ></span>
                    <span class="checkbox-value">{{ `(${item.count})` }}</span>
                </el-checkbox>
            </el-checkbox-group>
        </div>
        <div slot="reference" class="popover-trigger" @click="handleTriggerClick">
            <el-input
                ref="searchInput"
                v-model="searchText"
                :placeholder="placeholder"
                class="search-input"
                clearable
                @focus="handleInputFocus"
                @blur="handleInputBlur"
                @input="handleSearchInput"
                @click.stop
            />
            <!-- <i class="el-icon-arrow-down" :class="{ rotate: isOpen }"></i> -->
        </div>
    </el-popover>
</template>

<script>
export default {
    name: 'DropdownCheckBox',
    props: {
        placeholder: {
            type: String,
            default: '请输入'
        },
        options: {
            type: Array,
            default: () => []
        },
        value: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            isIndeterminate: false,
            isOpen: false,
            checkedItems: [],
            isInternalUpdate: false, // 标记是否为内部更新，避免循环
            searchText: '', // 搜索文本
            blurTimer: null // 用于延迟处理失焦事件
        };
    },
    computed: {
        checkAll: {
            get() {
                return this.checkedItems.length === this.options.length && this.options.length > 0;
            },
            set(val) {
                if (val) {
                    this.checkedItems = this.options.map((item) => item.name);
                } else {
                    this.checkedItems = [];
                }
                this.isIndeterminate = false;
            }
        },
        // 过滤后的选项列表
        filteredOptions() {
            if (!this.searchText.trim()) {
                return this.options;
            }
            const searchLower = this.searchText.toLowerCase();
            return this.options.filter((item) => item.name.toLowerCase().includes(searchLower));
        }
    },
    watch: {
        // 监听外部 value 变化，同步到内部 checkedItems
        value: {
            immediate: true,
            deep: true,
            handler(newVal) {
                if (this.isInternalUpdate) return; // 如果是内部更新触发的，跳过

                // 避免循环触发：只有当外部值与内部值不同时才更新
                if (JSON.stringify(newVal) !== JSON.stringify(this.checkedItems)) {
                    this.isInternalUpdate = true;
                    if (newVal && newVal.length > 0) {
                        this.checkedItems = [...newVal];
                    } else {
                        this.checkedItems = [];
                    }
                    this.$nextTick(() => {
                        this.isInternalUpdate = false;
                    });
                }
            }
        },
        // 当 options 改变全部选中
        options: {
            immediate: true,
            deep: true,
            handler(newOpts, oldOpts) {
                if (JSON.stringify(oldOpts) === JSON.stringify(newOpts)) {
                    return;
                }
                this.checkedItems = newOpts.map((item) => item.name);
            }
        },
        checkedItems: {
            immediate: true,
            deep: true,
            handler(val) {
                if (this.isInternalUpdate) return; // 如果是内部更新触发的，跳过

                // 总是向外部同步，让外部决定是否需要更新
                this.$emit('input', val);
            }
        }
    },
    methods: {
        handleCheckedItemsChange(value) {
            const checkedCount = value.length;
            if (checkedCount === this.options.length) {
                this.isIndeterminate = false;
            } else if (checkedCount === 0) {
                this.isIndeterminate = false;
            } else {
                this.isIndeterminate = true;
            }
        },
        // 处理触发器点击事件
        handleTriggerClick() {
            this.isOpen = !this.isOpen;
            if (this.isOpen) {
                this.$nextTick(() => {
                    this.$refs.searchInput.focus();
                });
            }
        },
        // 处理输入框获得焦点
        handleInputFocus() {
            if (this.blurTimer) {
                clearTimeout(this.blurTimer);
                this.blurTimer = null;
            }
            this.isOpen = true;
        },
        // 处理输入框失去焦点
        handleInputBlur() {
            // 延迟关闭下拉框，避免点击选项时立即关闭
            this.blurTimer = setTimeout(() => {
                this.isOpen = false;
            }, 200);
        },
        // 处理搜索输入
        handleSearchInput() {
            // 搜索时保持下拉框打开
            if (!this.isOpen) {
                this.isOpen = true;
            }
        },
        // 高亮匹配的文本
        highlightText(text) {
            if (!this.searchText.trim()) {
                return text;
            }
            const searchText = this.searchText.trim();
            const regex = new RegExp(`(${searchText})`, 'gi');
            return text.replace(regex, '<span class="highlight">$1</span>');
        }
    },
    beforeDestroy() {
        // 清理定时器
        if (this.blurTimer) {
            clearTimeout(this.blurTimer);
            this.blurTimer = null;
        }
    }
};
</script>

<style lang="less" scoped>
.dropdown-checkbox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 15px;
    white-space: nowrap;
}
.is-active {
    border-color: #409eff;
}
/deep/.el-popover__reference-wrapper {
    width: 100%;
}
.popover-trigger {
    width: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);

    .search-input {
        flex: 1;
        /deep/ .el-input__inner {
            border: none;
            padding: 0;
            height: auto;
            line-height: normal;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            background: transparent;

            &:focus {
                border: none;
                box-shadow: none;
            }
        }
    }

    .el-icon-arrow-down {
        color: #c0c4cc;
        transition: transform 0.3s;
        margin-left: 5px;
        flex-shrink: 0;

        &.rotate {
            transform: rotate(180deg);
        }
    }
}

.dropdown-item {
    width: fit-content;
    /deep/ .el-checkbox {
        margin-right: 0;
        display: flex;
        align-items: center;
        gap: 4px;
    }
    /deep/ .el-checkbox__label {
        padding: 0;
        display: flex;
        align-items: center;
        gap: 4px;
        flex: 1;
        min-width: 40px;
    }
    .checkbox-label {
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        flex: 1;
        --line-clamp: 1;
        overflow: hidden;
        display: -webkit-box;
        line-clamp: var(--line-clamp);
        -webkit-line-clamp: var(--line-clamp);
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;

        /deep/ .highlight {
            background-color: #fff566;
            color: #000;
            font-weight: 500;
        }
    }
    .checkbox-value {
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
    }
}
.checkbox-group {
    display: flex;
    flex-direction: column;
    width: max-content;
    max-width: 200px;
    height: 360px;
    overflow-y: auto;
    --bar-width: 4px;
    --bar-height: 4px;
    --thumb-color: #c9c9c9;
    &::-webkit-scrollbar {
        width: var(--bar-width);
        height: var(--bar-height);
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 8px;
        background: var(--thumb-color);
    }
    &::-webkit-scrollbar-track {
        /* 滚动条里面轨道 */
        border-radius: 8px;
        background: transparent;
    }
    &::-webkit-scrollbar-corner {
        background: rgba(0, 0, 0, 0);
    }
}
</style>
